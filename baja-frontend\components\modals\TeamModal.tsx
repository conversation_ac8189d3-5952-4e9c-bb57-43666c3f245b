'use client';

import React, { useState, useEffect } from 'react';
import Modal from '@/components/ui/Modal';
import Input from '@/components/ui/Input';
import Button from '@/components/ui/Button';
import LoadingSpinner from '@/components/ui/LoadingSpinner';
import { api } from '@/lib/api';
import { locationService, Negara, Provinsi, KabupatenKota } from '@/services/locationService';

interface TeamModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  team?: {
    id: number;
    name: string;
    negara: string;
    provinsi: string;
    kabupaten_kota: string;
  } | null;
}

const TeamModal: React.FC<TeamModalProps> = ({ isOpen, onClose, onSuccess, team }) => {
  const [formData, setFormData] = useState({
    name: '',
    negara: '',
    provinsi: '',
    kabupaten_kota: '',
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  // Location data
  const [negaraList, setNegaraList] = useState<Negara[]>([]);
  const [provinsiList, setProvinsiList] = useState<Provinsi[]>([]);
  const [kabupatenKotaList, setKabupatenKotaList] = useState<KabupatenKota[]>([]);
  const [loadingLocation, setLoadingLocation] = useState(false);

  useEffect(() => {
    if (isOpen) {
      fetchNegara();
    }
  }, [isOpen]);

  useEffect(() => {
    if (team && isOpen) {
      setFormData({
        name: team.name,
        negara: team.negara,
        provinsi: team.provinsi,
        kabupaten_kota: team.kabupaten_kota,
      });
      // Load provinsi and kabupaten when editing
      setTimeout(() => {
        if (team.negara) {
          fetchProvinsi(team.negara);
        }
        if (team.provinsi) {
          setTimeout(() => {
            fetchKabupatenKota(team.provinsi);
          }, 500);
        }
      }, 100);
    } else if (!team) {
      setFormData({
        name: '',
        negara: '',
        provinsi: '',
        kabupaten_kota: '',
      });
      setProvinsiList([]);
      setKabupatenKotaList([]);
    }
    setError('');
  }, [team, isOpen]);

  // Fetch negara when modal opens
  const fetchNegara = async () => {
    try {
      setLoadingLocation(true);
      const data = await locationService.getAllNegara();
      setNegaraList(data);
    } catch (error) {
      console.error('Error fetching negara:', error);
    } finally {
      setLoadingLocation(false);
    }
  };

  // Fetch provinsi when negara changes
  const fetchProvinsi = async (negaraId: string) => {
    if (!negaraId) {
      setProvinsiList([]);
      setKabupatenKotaList([]);
      return;
    }

    try {
      setLoadingLocation(true);
      const data = await locationService.getProvinsiByNegara(parseInt(negaraId));
      setProvinsiList(data);
      setKabupatenKotaList([]);
    } catch (error) {
      console.error('Error fetching provinsi:', error);
    } finally {
      setLoadingLocation(false);
    }
  };

  // Fetch kabupaten/kota when provinsi changes
  const fetchKabupatenKota = async (provinsiId: string) => {
    if (!provinsiId) {
      setKabupatenKotaList([]);
      return;
    }

    try {
      setLoadingLocation(true);
      const data = await locationService.getKabupatenKotaByProvinsi(parseInt(provinsiId));
      setKabupatenKotaList(data);
    } catch (error) {
      console.error('Error fetching kabupaten/kota:', error);
    } finally {
      setLoadingLocation(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      let response;
      if (team) {
        response = await api.put(`/kontingen/${team.id}`, formData);
      } else {
        response = await api.post('/kontingen', formData);
      }

      if (response.data.success) {
        onSuccess();
        onClose();
      } else {
        setError(response.data.message || 'Failed to save team');
      }
    } catch (error) {
      console.error('Error saving team:', error);
      setError('Failed to save team');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Handle location cascading
    if (name === 'negara') {
      setFormData(prev => ({
        ...prev,
        negara: value,
        provinsi: '',
        kabupaten_kota: ''
      }));
      fetchProvinsi(value);
    } else if (name === 'provinsi') {
      setFormData(prev => ({
        ...prev,
        provinsi: value,
        kabupaten_kota: ''
      }));
      fetchKabupatenKota(value);
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={team ? 'Edit Team' : 'Create Team'}
    >
      <form onSubmit={handleSubmit} className="space-y-4">
        {error && (
          <div className="bg-red-500/10 border border-red-500/30 rounded-lg p-3">
            <p className="text-red-400 text-sm">{error}</p>
          </div>
        )}

        <div>
          <label htmlFor="name" className="block text-sm font-medium text-gray-300 mb-2">
            Team Name *
          </label>
          <Input
            id="name"
            name="name"
            type="text"
            value={formData.name}
            onChange={handleChange}
            placeholder="Enter team name"
            required
            disabled={loading}
          />
        </div>

        <div>
          <label htmlFor="negara" className="block text-sm font-medium text-gray-300 mb-2">
            Country *
          </label>
          <select
            id="negara"
            name="negara"
            value={formData.negara}
            onChange={handleChange}
            required
            disabled={loading || loadingLocation}
            className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-gold-500 focus:border-transparent"
          >
            <option value="">Select Country</option>
            {negaraList.map((negara) => (
              <option key={negara.id} value={negara.id}>
                {negara.name}
              </option>
            ))}
          </select>
        </div>

        <div>
          <label htmlFor="provinsi" className="block text-sm font-medium text-gray-300 mb-2">
            Province *
          </label>
          <select
            id="provinsi"
            name="provinsi"
            value={formData.provinsi}
            onChange={handleChange}
            required
            disabled={loading || loadingLocation || !formData.negara}
            className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-gold-500 focus:border-transparent"
          >
            <option value="">Select Province</option>
            {provinsiList.map((provinsi) => (
              <option key={provinsi.id} value={provinsi.id}>
                {provinsi.name}
              </option>
            ))}
          </select>
        </div>

        <div>
          <label htmlFor="kabupaten_kota" className="block text-sm font-medium text-gray-300 mb-2">
            City/Regency *
          </label>
          <select
            id="kabupaten_kota"
            name="kabupaten_kota"
            value={formData.kabupaten_kota}
            onChange={handleChange}
            required
            disabled={loading || loadingLocation || !formData.provinsi}
            className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-gold-500 focus:border-transparent"
          >
            <option value="">Select City/Regency</option>
            {kabupatenKotaList.map((kabupatenKota) => (
              <option key={kabupatenKota.id} value={kabupatenKota.id}>
                {kabupatenKota.name}
              </option>
            ))}
          </select>
        </div>

        <div className="flex justify-end space-x-3 pt-4">
          <Button
            type="button"
            variant="secondary"
            onClick={onClose}
            disabled={loading}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            className="bg-gold-500 hover:bg-gold-600 text-black"
            disabled={loading}
          >
            {loading ? (
              <>
                <LoadingSpinner size="sm" className="mr-2" />
                {team ? 'Updating...' : 'Creating...'}
              </>
            ) : (
              team ? 'Update Team' : 'Create Team'
            )}
          </Button>
        </div>
      </form>
    </Modal>
  );
};

export default TeamModal;
